Microsoft Visual Studio Solution File, Format Version 12.00
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SmsGateway.Api", "SmsGateway.api\SmsGateway.api.csproj", "{583F1C8C-0352-4681-B367-BBECC5C3528C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SmsGateway.Core", "SmsGateway.Core\SmsGateway.Core.csproj", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SmsGateway.Database", "SmsGateway.Database\SmsGateway.Database.csproj", "{00000000-0000-0000-0000-000000000000}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SmsGateway.Services", "SmsGateway.Services\SmsGateway.Services.csproj", "{00000000-0000-0000-0000-000000000000}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SmsGateway.Providers.Mock", "SmsGateway.Providers.Mock\SmsGateway.Providers.Mock.csproj", "{00000000-0000-0000-0000-000000000000}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SmsGateway.Providers.Twilio", "SmsGateway.Providers.Twilio\SmsGateway.Providers.Twilio.csproj", "{00000000-0000-0000-0000-000000000000}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SmsGateway.Data", "SmsGateway.Data\SmsGateway.Data.csproj", "{57BBEAAA-1FE1-46E5-900B-0B705D3E754F}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{583F1C8C-0352-4681-B367-BBECC5C3528C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{583F1C8C-0352-4681-B367-BBECC5C3528C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{583F1C8C-0352-4681-B367-BBECC5C3528C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{583F1C8C-0352-4681-B367-BBECC5C3528C}.Release|Any CPU.Build.0 = Release|Any CPU
		{57BBEAAA-1FE1-46E5-900B-0B705D3E754F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{57BBEAAA-1FE1-46E5-900B-0B705D3E754F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{57BBEAAA-1FE1-46E5-900B-0B705D3E754F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{57BBEAAA-1FE1-46E5-900B-0B705D3E754F}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
EndGlobal
