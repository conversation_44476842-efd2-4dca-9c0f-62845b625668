<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.0"/>
        <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0"/>
        <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0"/>
        <PackageReference Include="Hangfire.Core" Version="1.8.14"/>
        <PackageReference Include="StackExchange.Redis" Version="2.8.16"/>
        <PackageReference Include="DotLiquid" Version="2.2.692"/>
        <PackageReference Include="BCrypt.Net-Next" Version="4.0.3"/>
        <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="7.0.0"/>
        <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="7.0.0"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\SmsGateway.Core\SmsGateway.Core.csproj" />
        <ProjectReference Include="..\SmsGateway.Database\SmsGateway.Database.csproj" />
    </ItemGroup>

</Project>
