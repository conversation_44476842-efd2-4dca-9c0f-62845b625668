@using Microsoft.AspNetCore.Components.Authorization
@using MudBlazor
@inherits LayoutView
@inject IJSRuntime JSRuntime

<MudThemeProvider @ref="@_mudThemeProvider" @bind-IsDarkMode="@_isDarkMode" />
<MudPopoverProvider />
<MudDialogProvider />
<MudSnackbarProvider />

<MudLayout>
    <MudAppBar Elevation="1">
        <MudIconButton Icon="Icons.Material.Filled.Menu" Color="Color.Inherit" Edge="Edge.Start" OnClick="@((e) => DrawerToggle())" />
        <MudSpacer />
        <MudText Typo="Typo.h5" Class="ml-3">SMS Gateway Portal</MudText>
        <MudSpacer />
        <MudIconButton Icon="@(_isDarkMode ? Icons.Material.Filled.Brightness7 : Icons.Material.Filled.Brightness4)" 
                       Color="Color.Inherit" 
                       OnClick="@ToggleDarkMode" 
                       Title="Toggle Dark Mode" />
        <AuthorizeView>
            <Authorized>
                <MudMenu Icon="Icons.Material.Filled.AccountCircle" Color="Color.Inherit" Direction="Direction.Bottom" OffsetX="true">
                    <MudMenuItem Icon="Icons.Material.Filled.Person">Profile</MudMenuItem>
                    <MudMenuItem Icon="Icons.Material.Filled.Settings">Settings</MudMenuItem>
                    <MudDivider />
                    <MudMenuItem Icon="Icons.Material.Filled.Logout" OnClick="Logout">Logout</MudMenuItem>
                </MudMenu>
            </Authorized>
            <NotAuthorized>
                <MudButton Variant="Variant.Text" Color="Color.Inherit" Href="/login">Login</MudButton>
            </NotAuthorized>
        </AuthorizeView>
    </MudAppBar>
    
    <MudDrawer @bind-Open="_drawerOpen" ClipMode="DrawerClipMode.Always" Elevation="2">
        <NavMenu />
    </MudDrawer>
    
    <MudMainContent Class="pt-16 px-16">
        <MudContainer Class="mt-6" MaxWidth="MaxWidth.False">
            @Body
        </MudContainer>
    </MudMainContent>
</MudLayout>

<BlazoredToasts Position="ToastPosition.TopRight" 
                Timeout="5" 
                IconType="IconType.FontAwesome" 
                SuccessClass="success-toast" 
                SuccessIcon="fas fa-check" 
                ErrorClass="error-toast" 
                ErrorIcon="fas fa-times" />

@code {
    private bool _drawerOpen = true;
    private bool _isDarkMode = false;
    private MudThemeProvider _mudThemeProvider = null!;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            _isDarkMode = await _mudThemeProvider.GetSystemPreference();
            StateHasChanged();
        }
    }

    private void DrawerToggle()
    {
        _drawerOpen = !_drawerOpen;
    }

    private async Task ToggleDarkMode()
    {
        _isDarkMode = !_isDarkMode;
    }

    private async Task Logout()
    {
        // Implement logout logic
        await JSRuntime.InvokeVoidAsync("location.reload");
    }
}
