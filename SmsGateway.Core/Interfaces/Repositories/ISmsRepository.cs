using SmsGateway.Core.Models;
using SmsGateway.Core.Models.Database;

namespace SmsGateway.Core.Interfaces.Repositories;

/// <summary>
/// Repository interface for SMS messages
/// </summary>
public interface ISmsMessageRepository : IRepository<SmsMessage>
{
    /// <summary>
    /// Get SMS messages by tenant ID
    /// </summary>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="status">Optional status filter</param>
    /// <param name="fromDate">Optional from date filter</param>
    /// <param name="toDate">Optional to date filter</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of SMS messages</returns>
    Task<IEnumerable<SmsMessage>> GetByTenantAsync(
        string tenantId,
        SmsStatus? status = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get SMS message by external message ID
    /// </summary>
    /// <param name="externalMessageId">External message ID</param>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>SMS message or null if not found</returns>
    Task<SmsMessage?> GetByExternalMessageIdAsync(
        string externalMessageId,
        string tenantId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get SMS messages by phone number
    /// </summary>
    /// <param name="phoneNumber">Phone number</param>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of SMS messages</returns>
    Task<IEnumerable<SmsMessage>> GetByPhoneNumberAsync(
        string phoneNumber,
        string tenantId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get SMS messages by template ID
    /// </summary>
    /// <param name="templateId">Template ID</param>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of SMS messages</returns>
    Task<IEnumerable<SmsMessage>> GetByTemplateAsync(
        Guid templateId,
        string tenantId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get SMS statistics for a tenant
    /// </summary>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="fromDate">From date</param>
    /// <param name="toDate">To date</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>SMS statistics</returns>
    Task<SmsStatistics> GetStatisticsAsync(
        string tenantId,
        DateTime fromDate,
        DateTime toDate,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update message status
    /// </summary>
    /// <param name="messageId">Message ID</param>
    /// <param name="status">New status</param>
    /// <param name="errorMessage">Error message if failed</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if updated successfully</returns>
    Task<bool> UpdateStatusAsync(
        Guid messageId,
        SmsStatus status,
        string? errorMessage = null,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for SMS templates
/// </summary>
public interface ISmsTemplateRepository : IRepository<SmsTemplate>
{
    /// <summary>
    /// Get template by name
    /// </summary>
    /// <param name="name">Template name</param>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Template or null if not found</returns>
    Task<SmsTemplate?> GetByNameAsync(
        string name,
        string tenantId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get templates by tenant ID
    /// </summary>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="category">Optional category filter</param>
    /// <param name="isActive">Optional active status filter</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of templates</returns>
    Task<IEnumerable<SmsTemplate>> GetByTenantAsync(
        string tenantId,
        string? category = null,
        bool? isActive = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get templates by category
    /// </summary>
    /// <param name="category">Category</param>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of templates</returns>
    Task<IEnumerable<SmsTemplate>> GetByCategoryAsync(
        string category,
        string tenantId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Increment template usage count
    /// </summary>
    /// <param name="templateId">Template ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task IncrementUsageCountAsync(Guid templateId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for SMS queue
/// </summary>
public interface ISmsQueueRepository : IRepository<SmsQueue>
{
    /// <summary>
    /// Get pending queue items
    /// </summary>
    /// <param name="maxItems">Maximum number of items to retrieve</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of pending queue items</returns>
    Task<IEnumerable<SmsQueue>> GetPendingItemsAsync(
        int maxItems = 100,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get queue items by status
    /// </summary>
    /// <param name="status">Queue status</param>
    /// <param name="tenantId">Optional tenant ID filter</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of queue items</returns>
    Task<IEnumerable<SmsQueue>> GetByStatusAsync(
        QueueStatus status,
        string? tenantId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update queue item status
    /// </summary>
    /// <param name="queueId">Queue item ID</param>
    /// <param name="status">New status</param>
    /// <param name="errorMessage">Error message if failed</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if updated successfully</returns>
    Task<bool> UpdateStatusAsync(
        Guid queueId,
        QueueStatus status,
        string? errorMessage = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Clean up old completed/failed items
    /// </summary>
    /// <param name="olderThan">Remove items older than this date</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of items cleaned up</returns>
    Task<int> CleanupOldItemsAsync(DateTime olderThan, CancellationToken cancellationToken = default);
}

/// <summary>
/// SMS statistics model
/// </summary>
public class SmsStatistics
{
    public string TenantId { get; set; } = string.Empty;
    public int TotalMessages { get; set; }
    public int SentMessages { get; set; }
    public int DeliveredMessages { get; set; }
    public int FailedMessages { get; set; }
    public int PendingMessages { get; set; }
    public decimal SuccessRate => TotalMessages > 0 ? (decimal)SentMessages / TotalMessages * 100 : 0;
    public decimal DeliveryRate => TotalMessages > 0 ? (decimal)DeliveredMessages / TotalMessages * 100 : 0;
    public decimal TotalCost { get; set; }
    public string Currency { get; set; } = "USD";
    public Dictionary<string, int> MessagesByProvider { get; set; } = new();
    public Dictionary<string, int> MessagesByStatus { get; set; } = new();
    public Dictionary<string, int> MessagesByDay { get; set; } = new();
}
