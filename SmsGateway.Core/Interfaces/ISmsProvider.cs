using SmsGateway.Core.Models;

namespace SmsGateway.Core.Interfaces;

/// <summary>
/// Interface that all SMS provider plugins must implement
/// </summary>
public interface ISmsProvider
{
    /// <summary>
    /// Unique identifier for this provider
    /// </summary>
    string Name { get; }

    /// <summary>
    /// Display name for this provider
    /// </summary>
    string DisplayName { get; }

    /// <summary>
    /// Version of the provider plugin
    /// </summary>
    string Version { get; }

    /// <summary>
    /// Description of the provider
    /// </summary>
    string Description { get; }

    /// <summary>
    /// Whether this provider is currently available/healthy
    /// </summary>
    bool IsAvailable { get; }

    /// <summary>
    /// Initialize the provider with configuration
    /// </summary>
    /// <param name="configuration">Provider-specific configuration</param>
    Task InitializeAsync(ProviderConfiguration configuration);

    /// <summary>
    /// Send an SMS message
    /// </summary>
    /// <param name="request">SMS request details</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>SMS response with delivery details</returns>
    Task<SmsResponse> SendSmsAsync(SmsRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get the status of a previously sent message
    /// </summary>
    /// <param name="messageId">Message ID returned from SendSmsAsync</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Current message status</returns>
    Task<SmsStatus> GetMessageStatusAsync(string messageId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if this provider supports sending to a specific country
    /// </summary>
    /// <param name="countryCode">ISO 3166-1 alpha-2 country code</param>
    /// <returns>True if supported, false otherwise</returns>
    bool SupportsCountry(string countryCode);

    /// <summary>
    /// Get the estimated cost for sending an SMS
    /// </summary>
    /// <param name="request">SMS request details</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Estimated cost and currency</returns>
    Task<(decimal cost, string currency)?> GetEstimatedCostAsync(SmsRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate the provider configuration
    /// </summary>
    /// <param name="configuration">Configuration to validate</param>
    /// <returns>Validation result with any error messages</returns>
    Task<ValidationResult> ValidateConfigurationAsync(ProviderConfiguration configuration);

    /// <summary>
    /// Perform a health check on the provider
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Health check result</returns>
    Task<HealthCheckResult> HealthCheckAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Cleanup resources when the provider is being unloaded
    /// </summary>
    Task DisposeAsync();
}

/// <summary>
/// Result of configuration validation
/// </summary>
public class ValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
}

/// <summary>
/// Result of a health check
/// </summary>
public class HealthCheckResult
{
    public bool IsHealthy { get; set; }
    public string? Message { get; set; }
    public Dictionary<string, object>? Data { get; set; }
    public TimeSpan ResponseTime { get; set; }
}
