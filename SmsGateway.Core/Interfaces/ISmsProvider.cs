using SmsGateway.Core.Models;
using SmsGateway.Core.Models.Common;
using SmsGateway.Core.Models.Requests;
using SmsGateway.Core.Models.Responses;
using ProviderConfiguration = SmsGateway.Core.Models.Common.ProviderConfiguration;

namespace SmsGateway.Core.Interfaces;

/// <summary>
/// Enhanced interface that all SMS provider plugins must implement
/// </summary>
public interface ISmsProvider : IAsyncDisposable
{
    #region Basic Properties
    /// <summary>
    /// Unique identifier for this provider
    /// </summary>
    string Name { get; }

    /// <summary>
    /// Display name for this provider
    /// </summary>
    string DisplayName { get; }

    /// <summary>
    /// Version of the provider plugin
    /// </summary>
    string Version { get; }

    /// <summary>
    /// Description of the provider
    /// </summary>
    string Description { get; }

    /// <summary>
    /// Provider author/vendor
    /// </summary>
    string Author { get; }

    /// <summary>
    /// Provider website URL
    /// </summary>
    string? WebsiteUrl { get; }

    /// <summary>
    /// Provider documentation URL
    /// </summary>
    string? DocumentationUrl { get; }

    /// <summary>
    /// Whether this provider is currently available/healthy
    /// </summary>
    bool IsAvailable { get; }

    /// <summary>
    /// Provider capabilities
    /// </summary>
    ProviderCapabilities Capabilities { get; }

    /// <summary>
    /// Provider limits and quotas
    /// </summary>
    ProviderLimits Limits { get; }
    #endregion

    #region Core SMS Operations
    /// <summary>
    /// Initialize the provider with configuration
    /// </summary>
    /// <param name="configuration">Provider-specific configuration</param>
    Task InitializeAsync(ProviderConfiguration configuration);

    /// <summary>
    /// Send a single SMS message
    /// </summary>
    /// <param name="request">SMS request details</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>SMS response with delivery details</returns>
    Task<SmsResponse> SendSmsAsync(SendSmsRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Send multiple SMS messages in a batch
    /// </summary>
    /// <param name="request">Bulk SMS request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Bulk SMS response</returns>
    Task<BulkSmsResponse> SendBulkSmsAsync(SendBulkSmsRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Schedule an SMS message for future delivery
    /// </summary>
    /// <param name="request">SMS request with scheduled time</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>SMS response</returns>
    Task<SmsResponse> ScheduleSmsAsync(SendSmsRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Cancel a scheduled SMS message
    /// </summary>
    /// <param name="messageId">Message ID to cancel</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if cancelled successfully</returns>
    Task<bool> CancelScheduledSmsAsync(string messageId, CancellationToken cancellationToken = default);
    #endregion

    #region Message Status and Tracking
    /// <summary>
    /// Get the status of a previously sent message
    /// </summary>
    /// <param name="messageId">Message ID returned from SendSmsAsync</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Current message status</returns>
    Task<MessageStatusResponse> GetMessageStatusAsync(string messageId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get detailed message information
    /// </summary>
    /// <param name="messageId">Message ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Detailed message information</returns>
    Task<MessageDetailsResponse> GetMessageDetailsAsync(string messageId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get delivery report for a message
    /// </summary>
    /// <param name="messageId">Message ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Delivery report</returns>
    Task<DeliveryReportResponse> GetDeliveryReportAsync(string messageId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get status of multiple messages
    /// </summary>
    /// <param name="messageIds">List of message IDs</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Status responses for each message</returns>
    Task<List<MessageStatusResponse>> GetBulkMessageStatusAsync(List<string> messageIds, CancellationToken cancellationToken = default);
    #endregion

    #region Cost and Pricing
    /// <summary>
    /// Get the estimated cost for sending an SMS
    /// </summary>
    /// <param name="request">SMS request details</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Estimated cost and currency</returns>
    Task<CostEstimateResponse> GetEstimatedCostAsync(GetCostEstimateRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get pricing information for different destinations
    /// </summary>
    /// <param name="countryCode">Country code (optional)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Pricing information</returns>
    Task<PricingInfoResponse> GetPricingInfoAsync(string? countryCode = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get account balance
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Account balance information</returns>
    Task<AccountBalanceResponse> GetAccountBalanceAsync(CancellationToken cancellationToken = default);
    #endregion

    #region Capabilities and Validation
    /// <summary>
    /// Check if this provider supports sending to a specific country
    /// </summary>
    /// <param name="countryCode">ISO 3166-1 alpha-2 country code</param>
    /// <returns>True if supported, false otherwise</returns>
    bool SupportsCountry(string countryCode);

    /// <summary>
    /// Check if provider supports a specific feature
    /// </summary>
    /// <param name="feature">Feature to check</param>
    /// <returns>True if supported</returns>
    bool SupportsFeature(ProviderFeature feature);

    /// <summary>
    /// Validate phone number format for this provider
    /// </summary>
    /// <param name="phoneNumber">Phone number to validate</param>
    /// <param name="countryCode">Country code</param>
    /// <returns>Validation result</returns>
    Task<PhoneValidationResponse> ValidatePhoneNumberAsync(string phoneNumber, string? countryCode = null);

    /// <summary>
    /// Validate message content
    /// </summary>
    /// <param name="message">Message content</param>
    /// <param name="messageType">Type of message</param>
    /// <returns>Validation result</returns>
    Task<MessageValidationResponse> ValidateMessageAsync(string message, MessageType messageType = MessageType.Text);

    /// <summary>
    /// Validate the provider configuration
    /// </summary>
    /// <param name="configuration">Configuration to validate</param>
    /// <returns>Validation result with any error messages</returns>
    Task<ValidationResult> ValidateConfigurationAsync(ProviderConfiguration configuration);
    #endregion

    #region Health and Monitoring
    /// <summary>
    /// Perform a health check on the provider
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Health check result</returns>
    Task<HealthCheckResult> HealthCheckAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get provider statistics
    /// </summary>
    /// <param name="fromDate">Start date for statistics</param>
    /// <param name="toDate">End date for statistics</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Provider statistics</returns>
    Task<ProviderStatisticsResponse> GetStatisticsAsync(DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get current rate limit status
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Rate limit information</returns>
    Task<RateLimitResponse> GetRateLimitStatusAsync(CancellationToken cancellationToken = default);
    #endregion

    #region Advanced Features
    /// <summary>
    /// Send SMS with template
    /// </summary>
    /// <param name="request">Template SMS request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>SMS response</returns>
    Task<SmsResponse> SendTemplateSmsAsync(SendTemplateSmsRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Send SMS with attachments (if supported)
    /// </summary>
    /// <param name="request">MMS request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>SMS response</returns>
    Task<SmsResponse> SendMmsAsync(SendMmsRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get inbox messages (if supported)
    /// </summary>
    /// <param name="request">Inbox request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Inbox messages</returns>
    Task<InboxResponse> GetInboxAsync(GetInboxRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Configure webhook for delivery reports
    /// </summary>
    /// <param name="webhookUrl">Webhook URL</param>
    /// <param name="events">Events to subscribe to</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Webhook configuration result</returns>
    Task<WebhookConfigResponse> ConfigureWebhookAsync(string webhookUrl, List<WebhookEvent> events, CancellationToken cancellationToken = default);
    #endregion
}
