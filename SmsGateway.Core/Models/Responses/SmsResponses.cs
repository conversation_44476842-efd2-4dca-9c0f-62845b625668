using SmsGateway.Core.Models.Common;

namespace SmsGateway.Core.Models.Responses;

/// <summary>
/// Response model for SMS sending operations
/// </summary>
public class SmsResponse
{
    /// <summary>
    /// Indicates if the SMS was successfully sent
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Unique message ID from the provider
    /// </summary>
    public string? MessageId { get; set; }

    /// <summary>
    /// Provider that handled the request
    /// </summary>
    public string Provider { get; set; } = string.Empty;

    /// <summary>
    /// Current status of the message
    /// </summary>
    public SmsStatus Status { get; set; }

    /// <summary>
    /// Error message if sending failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Error code if sending failed
    /// </summary>
    public string? ErrorCode { get; set; }

    /// <summary>
    /// Timestamp when the response was generated
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Cost of sending the message (if available)
    /// </summary>
    public decimal? Cost { get; set; }

    /// <summary>
    /// Currency of the cost
    /// </summary>
    public string? Currency { get; set; }

    /// <summary>
    /// Number of message segments used
    /// </summary>
    public int? Segments { get; set; }

    /// <summary>
    /// Additional metadata from the provider
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }

    /// <summary>
    /// Database message ID (if saved to database)
    /// </summary>
    public Guid? DatabaseMessageId { get; set; }

    /// <summary>
    /// Template ID used (if any)
    /// </summary>
    public Guid? TemplateId { get; set; }

    /// <summary>
    /// Original message before template processing
    /// </summary>
    public string? OriginalMessage { get; set; }

    /// <summary>
    /// Whether the message was queued for background processing
    /// </summary>
    public bool IsQueued { get; set; }

    /// <summary>
    /// Queue ID if message was queued
    /// </summary>
    public Guid? QueueId { get; set; }

    /// <summary>
    /// Tenant ID for multi-tenancy
    /// </summary>
    public string? TenantId { get; set; }
}

/// <summary>
/// Response model for bulk SMS operations
/// </summary>
public class BulkSmsResponse
{
    /// <summary>
    /// Overall success status
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Total number of messages processed
    /// </summary>
    public int TotalMessages { get; set; }

    /// <summary>
    /// Number of successfully sent messages
    /// </summary>
    public int SuccessfulMessages { get; set; }

    /// <summary>
    /// Number of failed messages
    /// </summary>
    public int FailedMessages { get; set; }

    /// <summary>
    /// Individual message responses
    /// </summary>
    public List<SmsResponse> MessageResponses { get; set; } = new();

    /// <summary>
    /// Batch processing ID
    /// </summary>
    public Guid? BatchId { get; set; }

    /// <summary>
    /// Total estimated cost
    /// </summary>
    public decimal? TotalCost { get; set; }

    /// <summary>
    /// Currency of the total cost
    /// </summary>
    public string? Currency { get; set; }

    /// <summary>
    /// Processing timestamp
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Response model for message status queries
/// </summary>
public class MessageStatusResponse
{
    /// <summary>
    /// Message ID
    /// </summary>
    public string MessageId { get; set; } = string.Empty;

    /// <summary>
    /// Current message status
    /// </summary>
    public SmsStatus Status { get; set; }

    /// <summary>
    /// Status description
    /// </summary>
    public string? StatusDescription { get; set; }

    /// <summary>
    /// Provider that sent the message
    /// </summary>
    public string Provider { get; set; } = string.Empty;

    /// <summary>
    /// When the message was sent
    /// </summary>
    public DateTime? SentAt { get; set; }

    /// <summary>
    /// When the message was delivered (if confirmed)
    /// </summary>
    public DateTime? DeliveredAt { get; set; }

    /// <summary>
    /// Last status update time
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Error message if failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Additional status metadata
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }
}

/// <summary>
/// Response model for cost estimation
/// </summary>
public class CostEstimateResponse
{
    /// <summary>
    /// Estimated cost
    /// </summary>
    public decimal Cost { get; set; }

    /// <summary>
    /// Currency of the cost
    /// </summary>
    public string Currency { get; set; } = string.Empty;

    /// <summary>
    /// Provider used for estimation
    /// </summary>
    public string Provider { get; set; } = string.Empty;

    /// <summary>
    /// Number of message segments
    /// </summary>
    public int Segments { get; set; }

    /// <summary>
    /// Cost per segment
    /// </summary>
    public decimal CostPerSegment { get; set; }

    /// <summary>
    /// Destination country
    /// </summary>
    public string? Country { get; set; }

    /// <summary>
    /// Estimation timestamp
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Response model for SMS history queries
/// </summary>
public class SmsHistoryResponse
{
    /// <summary>
    /// SMS messages
    /// </summary>
    public List<SmsMessageDto> Messages { get; set; } = new();

    /// <summary>
    /// Total count of messages (for pagination)
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// Current page number
    /// </summary>
    public int Page { get; set; }

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// Total number of pages
    /// </summary>
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);

    /// <summary>
    /// Whether there are more pages
    /// </summary>
    public bool HasNextPage => Page < TotalPages;

    /// <summary>
    /// Whether there are previous pages
    /// </summary>
    public bool HasPreviousPage => Page > 1;
}

/// <summary>
/// DTO for SMS message in responses
/// </summary>
public class SmsMessageDto
{
    /// <summary>
    /// Message ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// External message ID from provider
    /// </summary>
    public string? ExternalMessageId { get; set; }

    /// <summary>
    /// Recipient phone number
    /// </summary>
    public string To { get; set; } = string.Empty;

    /// <summary>
    /// Sender phone number or ID
    /// </summary>
    public string? From { get; set; }

    /// <summary>
    /// Original message content
    /// </summary>
    public string OriginalMessage { get; set; } = string.Empty;

    /// <summary>
    /// Processed message content
    /// </summary>
    public string ProcessedMessage { get; set; } = string.Empty;

    /// <summary>
    /// Template used (if any)
    /// </summary>
    public TemplateDto? Template { get; set; }

    /// <summary>
    /// Provider used
    /// </summary>
    public string Provider { get; set; } = string.Empty;

    /// <summary>
    /// Current status
    /// </summary>
    public SmsStatus Status { get; set; }

    /// <summary>
    /// Message priority
    /// </summary>
    public SmsPriority Priority { get; set; }

    /// <summary>
    /// Number of segments
    /// </summary>
    public int Segments { get; set; }

    /// <summary>
    /// Cost of the message
    /// </summary>
    public decimal? Cost { get; set; }

    /// <summary>
    /// Currency
    /// </summary>
    public string? Currency { get; set; }

    /// <summary>
    /// Error message if failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Error code if failed
    /// </summary>
    public string? ErrorCode { get; set; }

    /// <summary>
    /// Retry count
    /// </summary>
    public int RetryCount { get; set; }

    /// <summary>
    /// When created
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// When last updated
    /// </summary>
    public DateTime UpdatedAt { get; set; }

    /// <summary>
    /// When sent
    /// </summary>
    public DateTime? SentAt { get; set; }

    /// <summary>
    /// When delivered
    /// </summary>
    public DateTime? DeliveredAt { get; set; }

    /// <summary>
    /// Scheduled send time
    /// </summary>
    public DateTime? ScheduledAt { get; set; }

    /// <summary>
    /// Reference ID
    /// </summary>
    public string? Reference { get; set; }
}

/// <summary>
/// DTO for template in responses
/// </summary>
public class TemplateDto
{
    /// <summary>
    /// Template ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Template name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Template display name
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;
}
