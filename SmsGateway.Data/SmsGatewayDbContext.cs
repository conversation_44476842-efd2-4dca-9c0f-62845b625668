using Microsoft.EntityFrameworkCore;
using Finbuckle.MultiTenant;
using SmsGateway.Core.Models.Database;
using SmsGateway.Core.Models.MultiTenancy;

namespace SmsGateway.Data;

/// <summary>
/// Entity Framework DbContext for SMS Gateway
/// </summary>
public class SmsGatewayDbContext : MultiTenantDbContext
{
    public SmsGatewayDbContext(ITenantInfo tenantInfo) : base(tenantInfo)
    {
    }

    public SmsGatewayDbContext(ITenantInfo tenantInfo, DbContextOptions<SmsGatewayDbContext> options) : base(tenantInfo, options)
    {
    }

    /// <summary>
    /// SMS messages
    /// </summary>
    public DbSet<SmsMessage> SmsMessages { get; set; }

    /// <summary>
    /// SMS templates
    /// </summary>
    public DbSet<SmsTemplate> SmsTemplates { get; set; }

    /// <summary>
    /// SMS status history
    /// </summary>
    public DbSet<SmsStatusHistory> SmsStatusHistory { get; set; }

    /// <summary>
    /// SMS queue
    /// </summary>
    public DbSet<SmsQueue> SmsQueue { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure multi-tenancy
        modelBuilder.ConfigureMultiTenant();

        // Configure SmsMessage entity
        modelBuilder.Entity<SmsMessage>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.TenantId);
            entity.HasIndex(e => e.ExternalMessageId);
            entity.HasIndex(e => e.To);
            entity.HasIndex(e => e.Status);
            entity.HasIndex(e => e.CreatedAt);
            entity.HasIndex(e => e.ScheduledAt);
            entity.HasIndex(e => new { e.TenantId, e.Status });
            entity.HasIndex(e => new { e.TenantId, e.CreatedAt });

            entity.Property(e => e.OriginalMessage).IsRequired();
            entity.Property(e => e.ProcessedMessage).IsRequired();
            entity.Property(e => e.To).IsRequired().HasMaxLength(20);
            entity.Property(e => e.From).HasMaxLength(20);
            entity.Property(e => e.Provider).IsRequired().HasMaxLength(50);
            entity.Property(e => e.Currency).HasMaxLength(3);
            entity.Property(e => e.ErrorCode).HasMaxLength(50);
            entity.Property(e => e.Reference).HasMaxLength(255);
            entity.Property(e => e.OriginIpAddress).HasMaxLength(45);
            entity.Property(e => e.UserAgent).HasMaxLength(500);

            entity.Property(e => e.Cost).HasPrecision(10, 4);

            // Configure relationships
            entity.HasOne(e => e.Template)
                  .WithMany(t => t.Messages)
                  .HasForeignKey(e => e.TemplateId)
                  .OnDelete(DeleteBehavior.SetNull);

            entity.HasMany(e => e.StatusHistory)
                  .WithOne(h => h.SmsMessage)
                  .HasForeignKey(h => h.SmsMessageId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // Configure SmsTemplate entity
        modelBuilder.Entity<SmsTemplate>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.TenantId);
            entity.HasIndex(e => new { e.TenantId, e.Name }).IsUnique();
            entity.HasIndex(e => e.Category);
            entity.HasIndex(e => e.IsActive);
            entity.HasIndex(e => new { e.TenantId, e.IsActive });

            entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
            entity.Property(e => e.DisplayName).IsRequired().HasMaxLength(200);
            entity.Property(e => e.Description).HasMaxLength(500);
            entity.Property(e => e.Content).IsRequired();
            entity.Property(e => e.Category).HasMaxLength(50);
            entity.Property(e => e.Language).HasMaxLength(10);
            entity.Property(e => e.Tags).HasMaxLength(500);
            entity.Property(e => e.DefaultSenderId).HasMaxLength(20);
            entity.Property(e => e.PreferredProvider).HasMaxLength(50);
            entity.Property(e => e.CreatedBy).HasMaxLength(100);
            entity.Property(e => e.UpdatedBy).HasMaxLength(100);
        });

        // Configure SmsStatusHistory entity
        modelBuilder.Entity<SmsStatusHistory>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.TenantId);
            entity.HasIndex(e => e.SmsMessageId);
            entity.HasIndex(e => e.CreatedAt);
            entity.HasIndex(e => new { e.SmsMessageId, e.CreatedAt });

            entity.Property(e => e.Reason).HasMaxLength(500);
            entity.Property(e => e.ErrorCode).HasMaxLength(50);
        });

        // Configure SmsQueue entity
        modelBuilder.Entity<SmsQueue>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.TenantId);
            entity.HasIndex(e => e.SmsMessageId);
            entity.HasIndex(e => e.Status);
            entity.HasIndex(e => e.ProcessAt);
            entity.HasIndex(e => new { e.Status, e.ProcessAt });
            entity.HasIndex(e => new { e.TenantId, e.Status });
            entity.HasIndex(e => e.HangfireJobId);

            entity.Property(e => e.HangfireJobId).HasMaxLength(100);

            // Configure relationship
            entity.HasOne(e => e.SmsMessage)
                  .WithMany()
                  .HasForeignKey(e => e.SmsMessageId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // Configure value conversions for enums
        modelBuilder.Entity<SmsMessage>()
            .Property(e => e.Status)
            .HasConversion<int>();

        modelBuilder.Entity<SmsMessage>()
            .Property(e => e.Priority)
            .HasConversion<int>();

        modelBuilder.Entity<SmsStatusHistory>()
            .Property(e => e.PreviousStatus)
            .HasConversion<int?>();

        modelBuilder.Entity<SmsStatusHistory>()
            .Property(e => e.NewStatus)
            .HasConversion<int>();

        modelBuilder.Entity<SmsQueue>()
            .Property(e => e.Status)
            .HasConversion<int>();
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // Update timestamps
        var entries = ChangeTracker.Entries()
            .Where(e => e.State == EntityState.Added || e.State == EntityState.Modified);

        foreach (var entry in entries)
        {
            if (entry.Entity is SmsMessage message)
            {
                if (entry.State == EntityState.Added)
                {
                    message.CreatedAt = DateTime.UtcNow;
                }
                message.UpdatedAt = DateTime.UtcNow;
            }
            else if (entry.Entity is SmsTemplate template)
            {
                if (entry.State == EntityState.Added)
                {
                    template.CreatedAt = DateTime.UtcNow;
                }
                template.UpdatedAt = DateTime.UtcNow;
            }
            else if (entry.Entity is SmsQueue queue)
            {
                if (entry.State == EntityState.Added)
                {
                    queue.CreatedAt = DateTime.UtcNow;
                }
                queue.UpdatedAt = DateTime.UtcNow;
            }
        }

        return await base.SaveChangesAsync(cancellationToken);
    }
}
